const axios = require('axios');
const fs = require('fs');
const { extract_json } = require('../../shared/helpers');
const US_CITIES = require('../../shared/us_cities.json');


const US_AGENT_STATE_CITIES = {
    "AZ": ["Tucson"],
    "AR": ["Hot Springs"],
    "CA": ["Los Angeles", "San Diego", "San Francisco"],
    "CO": ["Aurora"],
    "CT": ["Stamford"],
    "DC": ["Washington"],
    "FL": ["Miami"],
    "GA": ["Atlanta"],
    "HI": ["Honolulu"],
    "IL": ["Chicago"],
    "LA": ["New Orleans"],
    "MA": ["Boston"],
    "MI": ["Detroit"],
    "MN": ["Minneapolis"],
    "NH": ["Portsmouth"],
    "NY": ["Buffalo", "New York"],
    "PA": ["Philadelphia"],
    "PR": ["San Juan"],
    "SC": ["North Charleston"],
    "TX": ["Dallas", "El Paso", "Houston"],
    "VT": ["St. Albans"],
    "WA": ["Seattle"]
};

const agent_passport_states = (req, res) => {
    res.json({
        sucess: true,
        type: 'select',
        data: Object.keys(US_AGENT_STATE_CITIES).map(v => ({
            text: US_CITIES.find(v => v.state_code === v).state_name,
            value: v,
        })),
    })
}

const agent_passport_cities = (req, res) => {
    const { appointment_desired_appointment_info_state } = req.query;
    res.json({
        success: true,
        type: 'select',
        data: US_AGENT_STATE_CITIES[appointment_desired_appointment_info_state || 'AZ'].map(v => ({
            text: v,
            value: v,
        })),
    })
}

module.exports = {
    agent_passport_states,
    agent_passport_cities,
}