const _ = require('lodash');
const moment = require('moment');
const { read_sheet } = require('../../shared/google');
const { ETS, ETSQueryPod, sequelize, ETSProvider, Organization } = require('../../shared/database');
const { PRODUCT_SHEET_ID } = require('./config');
const ENV = process.env.ad_env || 'stag';

const isValidJSON = (str) => {
    try {
        JSON.parse(str);
        return true;
    } catch (e) {
        return false;
    }
}
const router = {}
router.run_update_product = async (req, res) => {
    try {
        const master_sheets = await read_sheet(PRODUCT_SHEET_ID, 0, ['ets', 'pricing', 'special_working_times', 'ets_provider', 'corporation', 'promotion']);
        const sheet_ets = master_sheets.ets;

        const columns = ['id', 'name', 'service_type', 'country', 'airport', 'tasks', 'currency', 'status', 'tag', 'working_times']


        for (const row of sheet_ets) {
            const attributes = {
                processing_time: row.processing_time || '',
                terminal: row.terminal || '',
                receive_method: row.receive_method || '',
                product_info: row.product_info || null,
                single_application: row.single_application === 'TRUE',
                tracking: row.tracking || [],
                status_update: JSON.parse(row.status_update || '{}') || null,
                email_template: JSON.parse(row.email_template || '{}') || null,
                additional_rating: row.additional_rating || [],
                apply_for: row.apply_for || '',
                form: row.form || null,
                region_of_residence: row.region_of_residence || '',
                submit_method: row.submit_method || '',
                document_type: row.document_type || '',
                shipments: row.shipments || '',
                separate_shipment: row.separate_shipment === 'TRUE',
                user_age: row.user_age || '',
                process_type: row.process_type || '',
                photo_document: row.photo_document || null,
                condition: row.condition || '',
                upload: row.upload || null,
                nationality: row.nationality || '',
                transit_time: row.transit_time || '',
                validity: row.validity || '',
                number_of_entries: row.number_of_entries || '',
                purpose: row.purpose || '',
                category: row.category || '',
                urgent: row.urgent === 'TRUE',
                limitations: row.limitations || null,
                notifications: JSON.parse(row.notifications || '{}'),
            }

            const ets_product = await ETS.findOne({ where: { id: row.id } });
            for (const key of Object.keys(attributes)) {
                if (isValidJSON(attributes[key])) {
                    attributes[key] = JSON.parse(attributes[key])
                }
            }
            if (ets_product) {
                ets_product.attributes = attributes
                _.assign(ets_product, _.pick(row, columns))
                await ets_product.save();
            } else {
                await ETS.create({
                    ..._.pick(row, columns),
                    attributes,
                });
            }
        }


        for (const row of master_sheets.special_working_times) {
            const query = `
                INSERT INTO special_working_times (name, location, working_hours, special_off_days, query_time_setting, buffer)
                VALUES (:name, :location, :working_hours, :special_off_days, :query_time_setting, :buffer)
                ON CONFLICT (name) DO UPDATE 
                SET location = EXCLUDED.location,
                    working_hours = EXCLUDED.working_hours,
                    special_off_days = EXCLUDED.special_off_days,
                    query_time_setting = EXCLUDED.query_time_setting,
                    buffer = EXCLUDED.buffer;
            `;

            await sequelize.query(query, {
                replacements: {
                    name: row.name,
                    location: row.location,
                    working_hours: row.working_hours,
                    special_off_days: row.special_off_days,
                    query_time_setting: row.query_time_setting,
                    buffer: row.buffer,
                },
            });
        }

        for (const row of master_sheets.pricing) {
            const query = `
                INSERT INTO ets_price 
                (ets_id, provider_id, currency, price, additional_fee, shipments, price_modified_rules, additional_services, discount)
                VALUES (:ets_id, :provider_id, :currency, :price, :additional_fee, :shipments, :price_modified_rules, :additional_services, :discount)
                ON CONFLICT (ets_id) DO UPDATE 
                SET provider_id = EXCLUDED.provider_id,
                    currency = EXCLUDED.currency,
                    price = EXCLUDED.price,
                    additional_fee = EXCLUDED.additional_fee,
                    shipments = EXCLUDED.shipments,
                    price_modified_rules = EXCLUDED.price_modified_rules,
                    additional_services = EXCLUDED.additional_services,
                    discount = EXCLUDED.discount;
            `;

            await sequelize.query(query, {
                replacements: {
                    ets_id: row.ets_id,
                    provider_id: row.provider_id,
                    currency: row.currency,
                    price: parseInt(row.price),
                    additional_fee: row.additional_fee === '' ? '{}' : row.additional_fee,
                    shipments: row.shipments === '' ? '{}' : row.shipments,
                    price_modified_rules: row.price_modified_rules === '' ? '[]' : row.price_modified_rules,
                    additional_services: row.additional_services === '' ? '[]' : row.additional_services,
                    discount: row.discount !== '' ? parseInt(row.discount) : 0,
                },
            });
        }

        for (const row of master_sheets.promotion) {
            const query = `
                INSERT INTO promotion_codes 
                (promotion_code, promotion_type, discount_rules, currency, quantity, remain, active, start_date, end_date, created_at, updated_at)
                VALUES (:promotion_code, :promotion_type, :discount_rules, :currency, :quantity, :remain, :active, :start_date, :end_date, :created_at, :updated_at)
                ON CONFLICT ON CONSTRAINT promotion_codes_un DO UPDATE 
                SET discount_rules = EXCLUDED.discount_rules,
                    currency = EXCLUDED.currency,
                    quantity = EXCLUDED.quantity,
                    remain = EXCLUDED.remain,
                    active = EXCLUDED.active,
                    start_date = EXCLUDED.start_date,
                    end_date = EXCLUDED.end_date,
                    updated_at = EXCLUDED.updated_at;
            `;

            console.log(row);

            await sequelize.query(query, {
                replacements: {
                    promotion_code: row.promotion_code,
                    promotion_type: row.promotion_type,
                    discount_rules: typeof row.discount_rules === 'string' ? row.discount_rules : JSON.stringify(row.discount_rules || {}),
                    currency: row.currency || 'USD',
                    quantity: parseFloat(row.quantity) || 0,
                    remain: parseFloat(row.quantity) || 0, // Using quantity as initial remain value since it's not in sample data
                    active: row.active === 'FALSE' || row.active === false ? false : true,
                    start_date: row.start_date ? moment(row.start_date).toISOString() : moment().toISOString(),
                    end_date: row.end_date ? moment(row.end_date).toISOString() : moment().toISOString(),
                    created_at: moment().toISOString(),
                    updated_at: moment().toISOString()
                },
            });
        }
        for (const row of master_sheets.ets_provider) {
            const organization = await Organization.findOne({ where: { id: row.org_id } });
            if (organization) {
                organization.name = row.name
                await organization.save();
            } else {
                await Organization.create({
                    id: row.org_id,
                    name: row.name
                })
            }

            const ets_provider = await ETSProvider.findOne({ where: { id: row.id } });
            if (ets_provider) {
                ets_provider.status = 'active';
                ets_provider.name = row.name;
                ets_provider.country = row.country;
                ets_provider.timezone_name = row.timezone_name || '';
                ets_provider.address = {
                    "city": "",
                    "state": row.state || '',
                    "address": row.address || '',
                    "country": row.country || '',
                    "zip_code": row.zip_code || '',
                    "address_in_native": row.address_in_native || '',
                };
                ets_provider.contact = {
                    "email": ENV === 'stag' ? '<EMAIL>' : row.email || '',
                    "phone": row.phone || '',
                    "surname": row.surname || '',
                    "given_name": row.given_name || '',
                    "zalo_group": row.zalo_group || '',
                    "zalo_reaction_id": row.zalo_reaction_id || '',
                    "zalo_reaction_ad_email": row.zalo_reaction_ad_email || '',
                    "secondary_email": ENV === 'stag' ? '<EMAIL>' : row.secondary_email,
                    "secondary_phone": ENV === 'stag' ? '<EMAIL>' : row.secondary_phone,
                }

                ets_provider.secondary_contact = {}
                ets_provider.served_countries = row.served_countries?.split('')?.map(v => v.trim()) || []
                ets_provider.served_area = row.served_area || ''
                ets_provider.website = row.website || ''
                ets_provider.ad_contacts = []
                ets_provider.org_id = row.org_id
                ets_provider.served_services = row.served_services?.split(',')?.map(v => v.trim()) || []
                await ets_provider.save();
            } else {
                await ETSProvider.create({
                    id: row.id,
                    status: 'active',
                    name: row.name,
                    country: row.country,
                    timezone_name: row.timezone_name || '',
                    address: {
                        "city": "",
                        "state": row.state || '',
                        "address": row.address || '',
                        "country": row.country || '',
                        "zip_code": row.zip_code || '',
                        "address_in_native": row.address_in_native || '',
                    },
                    contact: {
                        "email": ENV === 'stag' ? '<EMAIL>' : row.email || '',
                        "phone": row.phone || '',
                        "surname": row.surname || '',
                        "given_name": row.given_name || '',
                        "zalo_group": row.zalo_group || '',
                        "zalo_reaction_id": row.zalo_reaction_id || '',
                        "zalo_reaction_ad_email": row.zalo_reaction_ad_email || '',
                        "secondary_email": ENV === 'stag' ? '<EMAIL>' : row.secondary_email,
                        "secondary_phone": ENV === 'stag' ? '<EMAIL>' : row.secondary_phone,
                    },
                    secondary_contact: {},
                    served_countries: row.served_countries?.split(',')?.map(v => v.trim()) || [],
                    served_area: row.served_area || '',
                    website: row.website || '',
                    ad_contacts: [],
                    org_id: row.org_id,
                    served_services: row.served_services?.split(',')?.map(v => v.trim()) || [],
                });
            }
        }
        console.log('DONE')

        res.json({ success: true })
    } catch (error) {
        console.log(error)
        res.json({ success: false, message: error.message })

    }
}

// router.run_update_product({ query: { service: null } }, { json: console.log })

module.exports = router