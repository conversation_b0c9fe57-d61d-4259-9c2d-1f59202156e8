package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"bitbucket.org/persistence17/aria/golang_services/models"
	"bitbucket.org/persistence17/aria/golang_services/sdk/whatsapp"
	"github.com/tidwall/gjson"
)

// ProcessJob process job
func (wd *WatchDogInstant) ProcessJob() error {
	var query = wd.dao.Db.PSQL.Select("id, job_name, job_data, schedule_at, started_at, completed_at, status, message,cron_interval, created_at, updated_at").
		From("watchdog_jobs").
		Where("status = ?", "pending").
		Where("schedule_at <= ?", time.Now()).
		// Where("job_name = ?", models.JobNameCronPassportStatusCheck).
		OrderBy("schedule_at ASC").
		Limit(1)

	sqlStr, args, _ := query.ToSql()

	var job models.WatchDogJob
	err := wd.dao.Db.Db.QueryRow(sqlStr, args...).Scan(
		&job.ID, &job.JobName, &job.JobData, &job.ScheduleAt, &job.StartedAt, &job.CompletedAt,
		&job.Status, &job.Message, &job.CronInterval, &job.CreatedAt, &job.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		return nil
	}

	if err != nil {
		return err
	}

	startedAt := time.Now()

	if job.JobName == models.JobNameDelayBeforeRemoveUserFromWhatsApp {
		group := gjson.ParseBytes(job.JobData).Get("group").String()
		phone := gjson.ParseBytes(job.JobData).Get("phone").String()
		if err := whatsapp.RemoveUsersFromGroup(group, []string{phone}); err != nil {
			fmt.Println(err)
		}
	}

	if job.JobName == models.JobNameDelaySendEmail {
		var mailTemplate = map[string]any{}
		json.Unmarshal(job.JobData, &mailTemplate)
		if err := wd.SendEmail(mailTemplate); err != nil {
			fmt.Println(err)
		}
	}

	if job.JobName == models.JobNameCronPassportStatusCheck {
		if err := wd.CheckAndGetUSPassportStatus(); err != nil {
			fmt.Println(err)
		}
	}

	if job.JobName == models.JobNameCronCheckAndSendFastlaneOrder {
		if err := wd.CheckAndSendNewFastlaneOrders(); err != nil {
			fmt.Println(err)
		}
	}

	if job.JobName == models.JobNameDelaySendUpdatedFastlaneOrder {
		orderID := gjson.ParseBytes(job.JobData).Get("order_id").Int()
		if err := wd.CheckAndSendUpdatedFastlaneOrder(int(orderID)); err != nil {
			fmt.Println(err)
		}
	}

	update := map[string]any{
		"status":       "completed",
		"started_at":   startedAt,
		"message":      "Job completed",
		"completed_at": time.Now(),
	}

	if job.CronInterval > 0 {
		update["status"] = "pending"
		update["schedule_at"] = job.ScheduleAt.Add(time.Duration(job.CronInterval) * time.Second)
	}

	// Update Job status to completed
	if _, err := wd.dao.Db.PSQL.Update("watchdog_jobs").
		SetMap(update).
		Where("id = ?", job.ID).
		RunWith(wd.dao.Db.Db).
		Exec(); err != nil {
		fmt.Println("Error updating job status:", err)
	}

	return nil
}
